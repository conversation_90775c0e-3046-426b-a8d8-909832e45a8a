<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arena Doviz Server Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>Arena Doviz Server Debug</h1>
    
    <div class="debug-section">
        <h2>Server Status</h2>
        <p class="success">✅ Server is running and serving this page</p>
        <p>Current URL: <span id="current-url"></span></p>
        <p>User Agent: <span id="user-agent"></span></p>
    </div>
    
    <div class="debug-section">
        <h2>Static Files Test</h2>
        <p>Testing if static files are loading...</p>
        <div id="static-test-results"></div>
    </div>
    
    <div class="debug-section">
        <h2>API Test</h2>
        <p>Testing API endpoints...</p>
        <div id="api-test-results"></div>
    </div>
    
    <div class="debug-section">
        <h2>Navigation Links</h2>
        <ul>
            <li><a href="/">Home/Dashboard</a></li>
            <li><a href="/accounts/login/">Login Page</a></li>
            <li><a href="/transactions/">Transactions</a></li>
            <li><a href="/customers/">Customers</a></li>
            <li><a href="/admin/">Admin</a></li>
        </ul>
    </div>
    
    <script>
        // Basic info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;
        
        // Test static files
        function testStaticFiles() {
            const staticTests = [
                '/static/css/arena-doviz.css',
                '/static/js/arena-doviz.js',
                '/static/js/bootstrap.bundle.min.js'
            ];
            
            const resultsDiv = document.getElementById('static-test-results');
            
            staticTests.forEach(url => {
                fetch(url)
                    .then(response => {
                        if (response.ok) {
                            resultsDiv.innerHTML += `<p class="success">✅ ${url}</p>`;
                        } else {
                            resultsDiv.innerHTML += `<p class="error">❌ ${url} (${response.status})</p>`;
                        }
                    })
                    .catch(error => {
                        resultsDiv.innerHTML += `<p class="error">❌ ${url} (Error: ${error.message})</p>`;
                    });
            });
        }
        
        // Test API endpoints
        function testAPI() {
            const apiTests = [
                '/api/v1/customers/customers/',
                '/api/v1/locations/locations/',
                '/api/v1/currencies/currencies/'
            ];
            
            const resultsDiv = document.getElementById('api-test-results');
            
            apiTests.forEach(url => {
                fetch(url)
                    .then(response => {
                        if (response.status === 401) {
                            resultsDiv.innerHTML += `<p class="warning">🔐 ${url} (Authentication required - OK)</p>`;
                        } else if (response.ok) {
                            resultsDiv.innerHTML += `<p class="success">✅ ${url}</p>`;
                        } else {
                            resultsDiv.innerHTML += `<p class="error">❌ ${url} (${response.status})</p>`;
                        }
                    })
                    .catch(error => {
                        resultsDiv.innerHTML += `<p class="error">❌ ${url} (Error: ${error.message})</p>`;
                    });
            });
        }
        
        // Run tests
        testStaticFiles();
        testAPI();
    </script>
</body>
</html>
